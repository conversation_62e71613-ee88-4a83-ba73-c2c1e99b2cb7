# Comment Deletion Fix - Testing Guide

## Test Scenarios

### Scenario 1: Basic Comment Deletion
1. Open PR schedule commenting modal
2. Add comments to 3 different schedules (e.g., Schedule A, B, C)
3. Save and verify all 3 comments appear in main textarea
4. Manually delete Schedule B comment from main textarea
5. Add a new comment for Schedule D
6. Save the form
7. **Expected Result**: Only Schedule A, C, and D comments should persist. Schedule B should NOT reappear.

### Scenario 2: Mixed Operations
1. Start with existing comments for Schedule X and Y
2. Delete Schedule X comment from textarea
3. Add new comment for Schedule Z
4. Edit Schedule Y comment in textarea
5. Save the form
6. **Expected Result**: Only Schedule Y (edited) and Z (new) should persist. Schedule X should NOT reappear.

### Scenario 3: Complete Deletion
1. Start with comments for multiple schedules
2. Delete ALL comments from main textarea (clear it completely)
3. Add one new comment for a different schedule
4. Save the form
5. **Expected Result**: Only the new comment should persist. All original comments should stay deleted.

## Debug Commands

Open browser console and use these commands to inspect state:

```javascript
// View current comment state
console.log('Comment State:', window.commentState);

// View what's deleted
console.log('Deleted IDs:', Array.from(window.commentState.deletedCommentIds));

// Debug sync issues
window.debugCommentSync();

// Reset state for testing
window.resetCommentState();
```

## Key Indicators of Success

1. **Deleted comments never reappear** after save operations
2. **New comments are properly added** and persist
3. **Edited comments maintain their changes**
4. **State tracking is accurate** (debug commands show correct state)
5. **Server receives correct data** (check network tab for pending_comments_data events)

## Troubleshooting

If issues persist:
1. Check browser console for JavaScript errors
2. Verify network requests include `deletedCommentIds` parameter
3. Ensure server logs show proper comment filtering
4. Use `window.debugCommentSync()` to inspect state mismatches
