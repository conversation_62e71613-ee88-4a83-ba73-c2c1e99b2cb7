// import chart from "../chart/amChart.js"
// import live_select from "live_select"

import Alpine from 'alpinejs';
// import live_select from "live_select"

// window.Alpine = Alpine;
// Alpine.start();

let Hooks = {},
    confirmParams = null;
Hooks.Credit = {
      mounted() {
        const input = this.el.querySelector('#credit-name');
        const dropdownMenu = this.el.querySelector('#credit-dropdown-menu');
        const toggleButton = this.el.querySelector('#credit-dropdown-toggle');
        let activeIndex = -1;

        const debounce = (func, delay) => {
          let timeout;
          return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
          };
        };

        toggleButton.addEventListener('click', () => {
          dropdownMenu.classList.toggle('hidden');
          if (!dropdownMenu.classList.contains('hidden')) {
            input.focus();
          }
        });

        const showNoResultsMessage = () => {
          let noResultsItem = dropdownMenu.querySelector('.no-results');
          if (!noResultsItem) {
            noResultsItem = document.createElement('li');
            noResultsItem.classList.add(
              'no-results',
              'cursor-default',
              'select-none',
              'relative',
              'py-2',
              'pl-3',
              'pr-9',
              'text-gray-500'
            );
            noResultsItem.textContent = 'No results found';
            dropdownMenu.querySelector('ul').appendChild(noResultsItem);
          }
          noResultsItem.classList.remove('hidden');
        };

        const hideNoResultsMessage = () => {
          const noResultsItem = dropdownMenu.querySelector('.no-results');
          if (noResultsItem) {
            noResultsItem.classList.add('hidden');
          }
        };

        const handleKeyup = debounce((event) => {
          const value = event.target.value.toLowerCase();
          let hasVisibleOptions = false;

          const listItems = this.el.querySelectorAll('#credit-dropdown-menu li:not(.no-results)');

          listItems.forEach((item, index) => {
            const optionText = item.textContent.toLowerCase();
            const optionValue = item.getAttribute('phx-value-value').toLowerCase();

            if (optionText.includes(value) || optionValue.includes(value)) {
              item.classList.remove('hidden');
              hasVisibleOptions = true;
            } else {
              item.classList.add('hidden');
            }
          });

          if (hasVisibleOptions) {
            dropdownMenu.classList.remove('hidden');
            hideNoResultsMessage();
          } else {
            dropdownMenu.classList.remove('hidden');
            showNoResultsMessage();
          }
        }, 300);

        input.addEventListener('keyup', handleKeyup);

        input.addEventListener('keydown', (event) => {
          const listItems = Array.from(dropdownMenu.querySelectorAll('li:not(.no-results):not(.hidden)'));

          switch (event.key) {
            case 'ArrowDown':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex + 1) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'ArrowUp':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex - 1 + listItems.length) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'Enter':
              event.preventDefault();
              if (activeIndex > -1 && listItems[activeIndex]) {
                listItems[activeIndex].click();
              }
              break;

            case 'Escape':
              event.preventDefault();
              dropdownMenu.classList.add('hidden');
              break;
          }
        });

        input.addEventListener('focus', () => {
          dropdownMenu.classList.remove('hidden');
        });

        dropdownMenu.querySelectorAll('li').forEach((item, index) => {
          item.addEventListener('click', (event) => {
            event.stopPropagation();

            const selectedValue = item.getAttribute('phx-value-value');
            const selectedName = item.getAttribute('phx-value-name');

            input.value = selectedName;
            dropdownMenu.classList.add('hidden');

            this.pushEvent('select_credit', {
              name: selectedName,
              value: selectedValue,
            });

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
          });
        });

        const highlightItem = (item) => {
          const listItems = dropdownMenu.querySelectorAll('li');
          listItems.forEach((li) => li.classList.remove('bg-indigo-600', 'text-white'));
          if (item) {
            item.scrollIntoView({ block: 'nearest' });
            item.classList.add('bg-indigo-600', 'text-white');
          }
        };
      },
};

Hooks.AutoDismissFlash = {
  mounted() {
    // Check if we've already shown this message
    if (this.el.dataset.shown === "true") {
      this.el.style.display = "none";
      return;
    }

    // Mark the message as shown
    this.el.dataset.shown = "true";

    // Automatically hide the flash message after 5 seconds
    setTimeout(() => {
      this.el.style.transition = "opacity 0.5s";
      this.el.style.opacity = 0;

      // Remove the element after the transition
      setTimeout(() => {
        this.el.remove();
        // Clear the flash from LiveView's storage
        this.pushEvent("lv:clear-flash");
      }, 500);
    }, 5000);
  }
};

Hooks.Debit = {
      mounted() {
        const input = this.el.querySelector('#debit-name');
        const dropdownMenu = this.el.querySelector('#debit-dropdown-menu');
        const toggleButton = this.el.querySelector('#debit-dropdown-toggle');
        let activeIndex = -1;

        const debounce = (func, delay) => {
          let timeout;
          return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
          };
        };

        // Show/hide dropdown manually with toggle button
        toggleButton.addEventListener('click', () => {
          dropdownMenu.classList.toggle('hidden');
          if (!dropdownMenu.classList.contains('hidden')) {
            input.focus();
          }
        });

        const showNoResultsMessage = () => {
          let noResultsItem = dropdownMenu.querySelector('.no-results');
          if (!noResultsItem) {
            noResultsItem = document.createElement('li');
            noResultsItem.classList.add(
              'no-results',
              'cursor-default',
              'select-none',
              'relative',
              'py-2',
              'pl-3',
              'pr-9',
              'text-gray-500'
            );
            noResultsItem.textContent = 'No results found';
            dropdownMenu.querySelector('ul').appendChild(noResultsItem);
          }
          noResultsItem.classList.remove('hidden');
        };

        const hideNoResultsMessage = () => {
          const noResultsItem = dropdownMenu.querySelector('.no-results');
          if (noResultsItem) {
            noResultsItem.classList.add('hidden');
          }
        };

        // Handle keyup to filter search results
        const handleKeyup = debounce((event) => {
          const value = event.target.value.toLowerCase();
          let hasVisibleOptions = false;

          const listItems = this.el.querySelectorAll('#debit-dropdown-menu li:not(.no-results)');


          listItems.forEach((item) => {
            const optionText = item.textContent.toLowerCase();
            const optionValue = item.getAttribute('phx-value-value').toLowerCase();

            if (optionText.includes(value) || optionValue.includes(value)) {
              item.classList.remove('hidden');
              hasVisibleOptions = true;
            } else {
              item.classList.add('hidden');
            }
          });

          if (hasVisibleOptions) {
            dropdownMenu.classList.remove('hidden');  // Ensure dropdown stays open
            hideNoResultsMessage();
          } else {
            dropdownMenu.classList.remove('hidden');
            showNoResultsMessage();
          }
        }, 300);

        input.addEventListener('keyup', handleKeyup);



        input.addEventListener('keydown', (event) => {
          const listItems = Array.from(dropdownMenu.querySelectorAll('li:not(.no-results):not(.hidden)'));

          switch (event.key) {
            case 'ArrowDown':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex + 1) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'ArrowUp':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex - 1 + listItems.length) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'Enter':
              event.preventDefault();
              if (activeIndex > -1 && listItems[activeIndex]) {
                listItems[activeIndex].click();
              }
              break;

            case 'Escape':
              event.preventDefault();
              dropdownMenu.classList.add('hidden');  // Close on Escape key
              break;
          }
        });

        // Show dropdown when input gets focus
        input.addEventListener('focus', () => {
          dropdownMenu.classList.remove('hidden');
        });

        // Click event to select an option
        dropdownMenu.querySelectorAll('li').forEach((item, index) => {
          item.addEventListener('click', (event) => {
            event.stopPropagation();

            const selectedValue = item.getAttribute('phx-value-value');
            const selectedName = item.getAttribute('phx-value-name');

            input.value = selectedName;
            dropdownMenu.classList.add('hidden');  // Close after selection

            this.pushEvent('select_debit', {
              name: selectedName,
              value: selectedValue,
            });

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
          });
        });

         // Close dropdown if clicked outside the dropdown or toggle button
         document.addEventListener('click', (event) => {
          if (!this.el.contains(event.target) && !toggleButton.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
          }
        });


        const highlightItem = (item) => {
          const listItems = dropdownMenu.querySelectorAll('li');
          listItems.forEach((li) => li.classList.remove('bg-indigo-600', 'text-white'));
          if (item) {
            item.scrollIntoView({ block: 'nearest' });
            item.classList.add('bg-indigo-600', 'text-white');
          }
        };
      },
};

Hooks.Dropdown = {
      mounted() {
        const input = this.el.querySelector('#template-name');
        const dropdownMenu = this.el.querySelector('#dropdown-menu');
        const toggleButton = this.el.querySelector('#dropdown-toggle');
        let activeIndex = -1; // Track the index of the highlighted option

        // this.clearError = function () {
        //   console.log('clearError called');
        // };

        const debounce = (func, delay) => {
          let timeout;
          return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
          };
        };

        // Show/hide dropdown manually with toggle button
        toggleButton.addEventListener('click', () => {
          dropdownMenu.classList.toggle('hidden');

          if (!dropdownMenu.classList.contains('hidden')) {
            input.focus();
          }
        });

        const showNoResultsMessage = () => {
          let noResultsItem = dropdownMenu.querySelector('.no-results');
          if (!noResultsItem) {
            noResultsItem = document.createElement('li');
            noResultsItem.classList.add(
              'no-results',
              'cursor-default',
              'select-none',
              'relative',
              'py-2',
              'pl-3',
              'pr-9',
              'text-gray-500'
            );
            noResultsItem.textContent = 'No results found';
            dropdownMenu.querySelector('ul').appendChild(noResultsItem);
          }
          noResultsItem.classList.remove('hidden');
        };

        const hideNoResultsMessage = () => {
          const noResultsItem = dropdownMenu.querySelector('.no-results');
          if (noResultsItem) {
            noResultsItem.classList.add('hidden');
          }
        };

        const handleKeyup = debounce((event) => {
          const value = event.target.value.toLowerCase();
          let hasVisibleOptions = false;

          const listItems = this.el.querySelectorAll('#dropdown-menu li:not(.no-results)');

          listItems.forEach((item, index) => {
            const optionText = item.textContent.toLowerCase();
            if (optionText.includes(value)) {
              item.classList.remove('hidden');
              hasVisibleOptions = true;
            } else {
              item.classList.add('hidden');
            }
          });

          if (hasVisibleOptions) {
            dropdownMenu.classList.remove('hidden');
            hideNoResultsMessage();
          } else {
            dropdownMenu.classList.remove('hidden');
            showNoResultsMessage();
          }
        }, 300);

        input.addEventListener('keyup', handleKeyup);

        document.addEventListener('click', (event) => {
          if (!this.el.contains(event.target) && !toggleButton.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
          }
        });

        input.addEventListener('keydown', (event) => {
          const listItems = Array.from(dropdownMenu.querySelectorAll('li:not(.no-results):not(.hidden)'));

          switch (event.key) {
            case 'ArrowDown':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex + 1) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'ArrowUp':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex - 1 + listItems.length) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'Enter':
              event.preventDefault();
              if (activeIndex > -1 && listItems[activeIndex]) {
                listItems[activeIndex].click();
              }
              break;

            case 'Escape':
              event.preventDefault();
              dropdownMenu.classList.add('hidden');
              break;
          }
        });

        input.addEventListener('focus', () => {
          dropdownMenu.classList.remove('hidden');
        });

        dropdownMenu.querySelectorAll('li').forEach((item, index) => {
          item.addEventListener('click', (event) => {
            event.stopPropagation();

            const selectedValue = item.getAttribute('phx-value-value');
            const selectedName = item.getAttribute('phx-value-name');

            input.value = selectedName;
            dropdownMenu.classList.add('hidden');

            this.pushEvent('select_report_type', {
              name: selectedName,
              value: selectedValue,
            });

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
          });
        });

        const highlightItem = (item) => {
          const listItems = dropdownMenu.querySelectorAll('li');
          listItems.forEach((li) => li.classList.remove('bg-indigo-600', 'text-white'));
          if (item) {
            item.scrollIntoView({ block: 'nearest' });
            item.classList.add('bg-indigo-600', 'text-white');
          }
        };
      },
};

Hooks.Dropdown2 = {
      mounted() {
        const input = this.el.querySelector('#select_gl_code');
        const dropdownMenu = this.el.querySelector('#dropdown-menu');
        const toggleButton = this.el.querySelector('#dropdown-toggle');
        const searchInput = this.el.querySelector('#dropdown-search');  // Search input inside dropdown
        let selectedItems = [];  // Store all selected items
        let maxVisibleItems = 4; // Maximum number of visible items
        let hiddenItemsCount = 0; // Number of hidden items

        // Toggle dropdown visibility when the button is clicked
        toggleButton.addEventListener('click', (e) => {
          e.stopPropagation();
          dropdownMenu.classList.toggle('hidden');
          searchInput.focus();
          this.requeryListItems();
        });


        // Attach event listeners to list items
        this.requeryListItems = () => {
          const listItems = this.el.querySelectorAll('#dropdown-menu li:not(.no-results)');

          listItems.forEach((item) => {
            item.addEventListener('click', (event) => {
              event.stopPropagation();

              const selectedValue = item.getAttribute("phx-value-value");
              const selectedName = item.getAttribute("phx-value-name");


              const exists = selectedItems.some(i => i.value === selectedValue);
              if (!exists) {
                selectedItems.push({ name: selectedName, value: selectedValue });


                setTimeout(() => {
                  this.updateInputField();  // Ensure input is updated after the click event is fully processed
                }, 1);


                this.pushEvent("select_gl_code", {
                  selectedItems: selectedItems.map(i => ({ name: i.name, value: i.value }))
                });

                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));


                input.focus();
              }
            });
          });
        };

        this.updateInputField = () => {


          const visibleLimit = 4;
          const totalItems = selectedItems.length;
          // console.log("Total items:", selectedItems);

          let displayItems;

          if (totalItems > visibleLimit) {
            const hiddenCount = totalItems - visibleLimit;

            displayItems = [`+${hiddenCount}`].concat(selectedItems.slice(-visibleLimit).map(i => i.value));
            console.log("Total items:", displayItems);

          } else {
            displayItems = selectedItems.map(i => i.value);
            console.log("Total items:", displayItems);
          }

        console.log(totalItems)


          input.value = displayItems.join(', ');
        };

      // final Backspace logic to delete a GL code based on cursor position
      input.addEventListener('keydown', (event) => {
        if (event.key === 'Backspace' && input.selectionStart === input.selectionEnd && input.value.length > 0) {
          event.preventDefault();

          const cursorPos = input.selectionStart;
          let displayCodes = input.value.split(', ');

          const plusXMatch = displayCodes[0].match(/^\+(\d+)$/);
          const hiddenCount = plusXMatch ? parseInt(plusXMatch[1]) : 0;

          if (hiddenCount > 0) {
            displayCodes = displayCodes.slice(1);
          }

          let cumulativeLength = 0;
          let deleteIndex = -1;

          for (let i = 0; i < displayCodes.length; i++) {
            cumulativeLength += displayCodes[i].length;

            // Add 2 for the ', ' separator except for the last item
            if (i < displayCodes.length - 1) {
              cumulativeLength += 2;
            }

            if (cumulativeLength >= cursorPos) {
              // Handle the edge case where the cursor is at the end of a GL code
              if (cumulativeLength === cursorPos || cursorPos === input.value.length) {
                deleteIndex = i; // Set deleteIndex to the current item
              } else {
                deleteIndex = i - 1; // Delete the previous item if cursor is between items
              }
              break;
            }
          }

          // Special case for cursor at the very end (after the last item)
          if (deleteIndex === -1 && cursorPos >= input.value.length) {
            deleteIndex = displayCodes.length - 1;
          }

          if (deleteIndex >= 0) {
            const actualIndex = hiddenCount + deleteIndex;

            if (cursorPos === input.value.length || cumulativeLength === cursorPos) {
              // If at the end of input or end of GL code, delete the current item
              selectedItems.splice(actualIndex, 1);
            } else if (actualIndex > 0) {
              // Delete the previous item if cursor is between items
              selectedItems.splice(actualIndex, 1);
            }

            this.updateInputField();

            this.pushEvent("select_gl_code", {
              selectedItems: selectedItems.map(i => ({ name: i.name, value: i.value }))
            });

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
          }
        }
      });

        // Handle search filter inside the dropdown
        searchInput.addEventListener('keyup', (event) => {
          const value = event.target.value.toLowerCase();
          let hasVisibleOptions = false;
          const listItems = this.el.querySelectorAll('#dropdown-menu li:not(.no-results)');

          listItems.forEach(item => {
            const optionText = item.textContent.toLowerCase();
            const optionValue = item.getAttribute('phx-value-value').toLowerCase();

            if (optionText.includes(value) || optionValue.includes(value)) {
              item.classList.remove('hidden');
              hasVisibleOptions = true;
            } else {
              item.classList.add('hidden');
            }
          });

          if (hasVisibleOptions) {
            dropdownMenu.classList.remove('hidden');
            this.hideNoResultsMessage();
          } else {
            dropdownMenu.classList.remove('hidden');
            this.showNoResultsMessage();
          }

          this.requeryListItems(); // Re-attach click events after filtering
        });

        // Close dropdown if clicked outside the dropdown or toggle button
        document.addEventListener('click', (event) => {
          if (!this.el.contains(event.target) && !toggleButton.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
          }
        });

        // Show or hide "No results found" message
        this.showNoResultsMessage = () => {
          let noResultsItem = dropdownMenu.querySelector('.no-results');
          if (!noResultsItem) {
            noResultsItem = document.createElement('li');
            noResultsItem.classList.add('no-results', 'cursor-default', 'select-none', 'relative', 'py-2', 'pl-3', 'pr-9', 'text-gray-500');
            noResultsItem.textContent = 'No results found';
            dropdownMenu.querySelector('ul').appendChild(noResultsItem);
          }
          noResultsItem.classList.remove('hidden');
        };

        this.hideNoResultsMessage = () => {
          const noResultsItem = dropdownMenu.querySelector('.no-results');
          if (noResultsItem) {
            noResultsItem.classList.add('hidden');
          }
        };

        input.addEventListener('keydown', (event) => {
          const listItems = Array.from(dropdownMenu.querySelectorAll('li:not(.no-results):not(.hidden)'));

          switch (event.key) {
            case 'ArrowDown':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex + 1) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'ArrowUp':
              event.preventDefault();
              if (listItems.length > 0) {
                activeIndex = (activeIndex - 1 + listItems.length) % listItems.length;
                highlightItem(listItems[activeIndex]);
              }
              break;

            case 'Enter':
              event.preventDefault();
              if (activeIndex > -1 && listItems[activeIndex]) {
                listItems[activeIndex].click();
              }
              break;

            case 'Escape':
              event.preventDefault();
              dropdownMenu.classList.add('hidden');
              break;
          }
        });

        const highlightItem = (item) => {
          const listItems = dropdownMenu.querySelectorAll('li');
          listItems.forEach((li) => li.classList.remove('bg-indigo-600', 'text-white'));
          if (item) {
            item.scrollIntoView({ block: 'nearest' });
            item.classList.add('bg-indigo-600', 'text-white');
          }
        };
      }
};

Hooks.Notification = {
  alert(type, text) {
    var notification = document.getElementById(type + '-notification')
    var panel = document.getElementById(type + '-notification-panel');
    var msg = document.getElementById(type + '-notification-msg');
    panel.classList.remove('translate-y-2', 'opacity-0', 'sm:translate-y-0', 'sm:translate-x-2');
    panel.classList.add('translate-y-0', 'opacity-100', 'sm:translate-x-0');
    notification.classList.remove('hidden')
    msg.textContent = text;
  },
  mounted() {
    this.handleEvent("notification", ({message}) => {
      var btn_close_modal = document.getElementById('modal-close');
      if (btn_close_modal) {
        btn_close_modal.click();
      }

      if (message.info) {
        this.alert('info', message.info);
        if (message.push_to) {
          this.pushEvent(message.push_to, message.params)
        }
      } else {
        this.alert('error', message.error);
      }
    })
  }
}

Hooks.HandleConfirmation = {
  params() { return this.el.dataset },
  mounted(){
    this.el.addEventListener("click", e => {
      if (this.params().isconfirm == 'YES') {
        this.el.disabled = true;
        document.getElementById('modal-confirm-msg').classList.add('hidden');
        document.getElementById('modal-processing-msg').classList.remove('hidden');
        this.pushEvent(confirmParams.event, confirmParams)
      }
    })
  }
}

Hooks.DownloadAccounts = {
  params() { return this.el.dataset },
  mounted(){
    this.el.addEventListener("click", e => {
      console.log([this.params().accounts])
      console.log(this.params)



          // JSON list
          const jsonArray = this.params().accounts;

            event.preventDefault(); // Prevent the default anchor behavior

            // Create a form element dynamically
            const form = document.createElement("form");
            form.method = "POST";   // Set the method to POST
            form.action = "/download/unmaintained/accounts"; // Set the target URL (replace '/submit' with your actual endpoint)

            // Add any input fields to the form if needed
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = "accounts";
            input.value = document.getElementById('accounts').value; // Set a sample value, this can be dynamic
            form.appendChild(input);

            const input2 = document.createElement("input");
            input2.type = "hidden";
            input2.name = "_csrf_token";
            input2.value =  document.getElementById('csrf').value; // Set a sample value, this can be dynamic
            form.appendChild(input2);


            // Append the form to the body and submit it
            document.body.appendChild(form);
            form.submit();

    })
  }
}

Hooks.DownloadGls = {
  params() { return this.el.dataset },
  mounted(){
    this.el.addEventListener("click", e => {
      console.log([this.params().accounts])
      console.log(this.params)



          // JSON list
          const jsonArray = this.params().accounts;

            event.preventDefault(); // Prevent the default anchor behavior

            // Create a form element dynamically
            const form = document.createElement("form");
            form.method = "POST";   // Set the method to POST
            form.action = "/download/unmaintained/gls"; // Set the target URL (replace '/submit' with your actual endpoint)

            // Add any input fields to the form if needed
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = "accounts";
            input.value = document.getElementById('accounts').value; // Set a sample value, this can be dynamic
            form.appendChild(input);

            const input2 = document.createElement("input");
            input2.type = "hidden";
            input2.name = "_csrf_token";
            input2.value =  document.getElementById('csrf').value; // Set a sample value, this can be dynamic
            form.appendChild(input2);


            // Append the form to the body and submit it
            document.body.appendChild(form);
            form.submit();

    })
  }
}

Hooks.Confirm = {
  params() { return this.el.dataset },
  mounted(){
    this.el.addEventListener("click", e => {
      confirmParams = this.params();
    })
  }
}

Hooks.CommentAuth = {
  params() { return this.el.dataset },
  mounted(){
    console.log('CommentAuth hook mounted for element:', this.el);
    this.el.addEventListener("click", e => {
      console.log('Comment button clicked!', e);
      e.preventDefault();
      e.stopPropagation();

      const schedule = this.el.dataset.schedule;
      console.log('Schedule:', schedule);

      if (!schedule) {
        console.error('No schedule data found on element');
        return;
      }

      const scheduleName = schedule.includes('schedule_') ?
        `Schedule ${schedule.replace('schedule_', '').toUpperCase()}` :
        schedule;
      console.log('Schedule name:', scheduleName);

      // Find the modal element
      const modal = document.getElementById('comment-modal');
      if (!modal) {
        console.error('Comment modal not found in DOM');
        return;
      }

      // Show the modal directly
      console.log('Showing modal directly');
      this.showModal(modal, schedule, scheduleName);

      // Also dispatch the custom event as backup
      console.log('Dispatching show-comment-modal event');
      window.dispatchEvent(new CustomEvent('show-comment-modal', {
        detail: {
          schedule: schedule,
          scheduleName: scheduleName
        }
      }));
      console.log('Event dispatched');
    });
  },

  showModal(modal, schedule, scheduleName) {
    console.log('showModal called with:', { modal, schedule, scheduleName });

    // Set the schedule value in the hidden input
    const scheduleInput = modal.querySelector('input[name="schedule"]');
    if (scheduleInput) {
      scheduleInput.value = schedule;
      console.log('Set schedule input value:', schedule);
    } else {
      console.warn('Schedule input not found');
    }

    // Update the modal title
    const modalTitle = modal.querySelector('#modal-title');
    if (modalTitle) {
      modalTitle.textContent = `Comment on ${scheduleName}`;
      console.log('Updated modal title:', modalTitle.textContent);
    } else {
      console.warn('Modal title not found');
    }

    // Clear the comment textarea
    const modalCommentTextarea = modal.querySelector('#modal-comment');
    if (modalCommentTextarea) {
      modalCommentTextarea.value = '';
      console.log('Cleared comment textarea');
    } else {
      console.warn('Modal comment textarea not found');
    }

    // Show the modal with multiple approaches
    modal.style.display = 'block';
    modal.style.visibility = 'visible';
    modal.classList.remove('hidden');

    console.log('Modal display style set to:', modal.style.display);
    console.log('Modal visibility set to:', modal.style.visibility);
    console.log('Modal classes:', modal.className);

    // Focus on the textarea
    if (modalCommentTextarea) {
      setTimeout(() => {
        modalCommentTextarea.focus();
      }, 100);
    }
  }
}

Hooks.Changestate = {
  params() {
    return this.el.dataset;  // Return dataset parameters
  },

  mounted() {
    this.el.addEventListener("click", e => {
      // Grab DOM elements by their ID
      const type_debit = document.getElementById('type_debit');
      const type_credit = document.getElementById('type_credit');
      const business_unit_credit = document.getElementById('business_unit_credit');
      const cur_cat_credit = document.getElementById('cur_cat_credit');
      const business_unit_debit = document.getElementById('business_unit_debit');
      const cur_cat_debit = document.getElementById('cur_cat_debit');

      // Check the value of the debit select element
      if (type_debit.value === "SAP") {
        business_unit_debit.disabled = true;
        cur_cat_debit.disabled = true;
      } else {
        business_unit_debit.disabled = false;
        cur_cat_debit.disabled = false;
      }

      // Check the value of the credit select element
      if (type_credit.value === "SAP") {
        business_unit_credit.disabled = true;
        cur_cat_credit.disabled = true;
      } else {
        business_unit_credit.disabled = false;
        cur_cat_credit.disabled = false;
      }
    });
  }
};


Hooks.PrintPage = {
  mounted() {
    var mywindow = window.open('', 'PRINT', 'height=400,width=600');

    mywindow.document.write('<html><head><title>' + document.title  + '</title>');
    mywindow.document.write('</head><body >');
    mywindow.document.write('<h1>' + document.title  + '</h1>');
    mywindow.document.write(document.getElementById(elem).innerHTML);
    mywindow.document.write('</body></html>');

    mywindow.document.close(); // necessary for IE >= 10
    mywindow.focus(); // necessary for IE >= 10*/

    mywindow.print();
    mywindow.close();
  }
}

// Hooks.DownloadQuarterly = {
//   params() { return this.el.dataset },
//   mounted(){
//     this.el.addEventListener("click", e => {
//       console.log('-------------------')


//       const element = document.getElementById('pdf-content');
//       const opt = {
//         margin: 0.5,
//         filename: 'tailwind-template.pdf',
//         image: { type: 'jpeg', quality: 0.98 },
//         html2canvas: { scale: 2 },
//         jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
//       };
//       html2pdf().set(opt).from(element).save();

//     })
//   }
// }




Hooks.UserRoleCheckboxes = {
  mounted() {
    this.updateCheckboxes();
  },

  updated() {
    this.updateCheckboxes();
  },

  updateCheckboxes() {
    let checkboxes = this.el.querySelectorAll('[data-select-val]');

    checkboxes.forEach((checkbox) => {
      let selectVal = checkbox.getAttribute('data-select-val');
      checkbox.checked = selectVal === 'Y';
    });
  },
};

// Add this hook to your existing hooks
Hooks.DownloadFile = {
  mounted() {
    console.log("DownloadFile hook mounted");

    this.handleEvent("download-file", ({ filename, content, content_type }) => {
      console.log("Download event received:", filename);

      const exportButton = this.el;
      const originalText = exportButton.innerText;

      // Temporarily disable button
      exportButton.innerText = "Exporting...";
      exportButton.disabled = true;

      try {
        // Create blob from base64
        const binaryString = atob(content);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: content_type });

        // Create download link
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);

        // Reset button after a short delay
        setTimeout(() => {
          exportButton.innerText = originalText;
          exportButton.disabled = false;
          console.log("Export button reset");
        }, 1000);
      } catch (error) {
        console.error("Download error:", error);
        // Reset button on error
        exportButton.innerText = originalText;
        exportButton.disabled = false;
      }
    });
  }
};

// Helper function to convert base64 to blob
function base64ToBlob(base64, contentType) {
  const binaryString = window.atob(base64);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return new Blob([bytes], { type: contentType });
}

// Helper function to convert base64 to blob
function base64ToBlob(base64, contentType) {
  const binaryString = window.atob(base64);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return new Blob([bytes], { type: contentType });
}


Hooks.CommentModal = {
  mounted() {
    console.log('CommentModal hook mounted for element:', this.el);
    // Get the modal element
    const modal = this.el;
    const scheduleInput = modal.querySelector('input[name="schedule"]');
    const modalTitle = modal.querySelector('#modal-title');
    const commentTextarea = modal.querySelector('textarea[name="comment"]');

    console.log('Modal elements found:', { modal, scheduleInput, modalTitle, commentTextarea });

    // Initialize structured comment state management
    if (!window.commentState) {
      window.commentState = {
        originalComments: {},      // Comments that existed when modal was first loaded
        deletedCommentIds: new Set(), // Schedule IDs of deleted comments
        currentComments: {},       // Active comments for display
        isInitialized: false       // Track if we've loaded original state
      };
    }

    // Maintain backward compatibility
    if (!window.pendingComments) {
      window.pendingComments = {};
    }

    // Add a global function to show modal (for debugging)
    window.showCommentModal = (schedule, scheduleName) => {
      console.log('Global showCommentModal called:', { schedule, scheduleName });
      this.showModalInternal(modal, schedule, scheduleName);
    };

    // Listen for the custom event to show the modal
    const showModalHandler = (event) => {
      console.log('CommentModal received show-comment-modal event:', event);
      const { schedule, scheduleName } = event.detail;
      this.showModalInternal(modal, schedule, scheduleName);
    };

    // Remove any existing listeners first
    window.removeEventListener('show-comment-modal', showModalHandler);
    window.addEventListener('show-comment-modal', showModalHandler);

    // Add the showModalInternal method
    this.showModalInternal = (modal, schedule, scheduleName) => {
      console.log('showModalInternal called with:', { modal, schedule, scheduleName });

      // Set the schedule value in the hidden input
      const scheduleInput = modal.querySelector('input[name="schedule"]');
      if (scheduleInput) {
        scheduleInput.value = schedule;
        console.log('Set schedule input value:', schedule);
      }

      // Update the modal title
      const modalTitle = modal.querySelector('#modal-title');
      if (modalTitle) {
        modalTitle.textContent = `Comment on ${scheduleName}`;
        console.log('Updated modal title');
      }

      // **SYNC FIX: Check if schedule still exists in main textarea before showing old comment**
      const modalCommentTextarea = modal.querySelector('#modal-comment');
      if (modalCommentTextarea) {
        // First, sync pending comments with current main textarea content
        const mainTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
        if (mainTextarea && this.parseCommentsFromTextarea) {
          const currentComments = this.parseCommentsFromTextarea(mainTextarea.value);
          window.pendingComments = currentComments;
        }

        // Now check if we have a current comment for this schedule
        if (window.pendingComments && window.pendingComments[schedule]) {
          modalCommentTextarea.value = window.pendingComments[schedule];
        } else {
          modalCommentTextarea.value = '';
        }
        console.log('Set comment textarea value after sync check');
      }

      // Show the modal with multiple approaches
      modal.style.display = 'block';
      modal.style.visibility = 'visible';
      modal.classList.remove('hidden');

      console.log('Modal shown - display:', modal.style.display, 'visibility:', modal.style.visibility);

      // Focus on the textarea
      if (modalCommentTextarea) {
        setTimeout(() => {
          modalCommentTextarea.focus();
        }, 100);
      }
    };

    // Add event listener to the save button
    const saveButton = modal.querySelector('#save-comment-button');
    if (saveButton) {
      saveButton.addEventListener('click', (e) => {
        console.log('Save button clicked, form will be submitted');
      });
    }

    // Get the cancel button and add multiple selectors
    const cancelButton = modal.querySelector('button[phx-click="cancel_comment"]') ||
                        modal.querySelector('button[onclick*="cancel"]') ||
                        modal.querySelector('.cancel-button');

    // Add event listener to the cancel button
    if (cancelButton) {
      cancelButton.addEventListener('click', () => {
        this.hideModal(modal);
      });
    }

    // Close the modal when clicking outside of it
    modal.addEventListener('click', (event) => {
      if (event.target === modal) {
        this.hideModal(modal);
      }
    });

    // Close the modal when pressing Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && modal.style.display === 'block') {
        this.hideModal(modal);
      }
    });

    // Add the hideModal method
    this.hideModal = (modal) => {
      console.log('hideModal called');
      modal.style.display = 'none';
      modal.style.visibility = 'hidden';
      modal.classList.add('hidden');

      // Clear the form
      const modalCommentTextarea = modal.querySelector('#modal-comment');
      if (modalCommentTextarea) {
        modalCommentTextarea.value = '';
      }

      const scheduleInput = modal.querySelector('input[name="schedule"]');
      if (scheduleInput) {
        scheduleInput.value = '';
      }

      console.log('Modal hidden');
    };

    // Handle the hide-comment-modal event from the server
    this.handleEvent('hide-comment-modal', () => {
      console.log('Received hide-comment-modal event');
      this.hideModal(modal);
    });

    // Handle the clear-pending-comments event from the server
    this.handleEvent('clear-pending-comments', () => {
      console.log('Clearing all pending comments');
      // Clear all pending comments when the form is approved or rejected
      window.pendingComments = {};
      console.log('Successfully cleared all pending comments');
    });

    // **SYNC FIX: Handle individual comment deletion**
    this.handleEvent('remove-schedule-comment', (data) => {
      const { schedule } = data;
      if (window.pendingComments && window.pendingComments[schedule]) {
        delete window.pendingComments[schedule];
        console.log(`Removed comment for schedule: ${schedule}`);

        // **ENHANCED: Update main textarea using current comment state**
        const mainTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
        if (mainTextarea) {
          // Use current comments (excludes deleted ones)
          const updatedComments = Object.entries(window.commentState.currentComments).map(([sched, comment]) => {
            const schedName = this.formatScheduleName(sched);
            return `[${schedName}] - ${comment}`;
          }).join('\n');

          mainTextarea.value = updatedComments;

          // Maintain backward compatibility
          window.pendingComments = window.commentState.currentComments;

          // **ENHANCED: Send complete state to server including deletion info**
          this.pushEvent('pending_comments_data', {
            comments: JSON.stringify(window.commentState.currentComments),
            deletedCommentIds: JSON.stringify(Array.from(window.commentState.deletedCommentIds)),
            originalComments: JSON.stringify(window.commentState.originalComments)
          });
        }
      }
    });

    // Handle the get-pending-comments event from the server
    this.handleEvent('get-pending-comments', () => {
      console.log('Getting all pending comments');

      // **ENHANCED: Initialize and sync comment state**
      const mainCommentTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
      if (mainCommentTextarea) {
        const currentTextareaValue = mainCommentTextarea.value || '';

        // Initialize original state if not done yet
        if (!window.commentState.isInitialized) {
          this.parseCommentsFromTextarea(currentTextareaValue, true);
        } else {
          // Parse current textarea content to detect manual changes
          const currentParsed = this.parseCommentsFromTextarea(currentTextareaValue);

          // Detect deletions by comparing with original comments
          Object.keys(window.commentState.originalComments).forEach(scheduleId => {
            if (!currentParsed[scheduleId]) {
              // Comment was deleted manually
              window.commentState.deletedCommentIds.add(scheduleId);
              console.log(`Detected manual deletion of comment for schedule: ${scheduleId}`);
            }
          });

          // Update current comments (excluding deleted ones)
          window.commentState.currentComments = currentParsed;
        }

        // Maintain backward compatibility
        window.pendingComments = window.commentState.currentComments;

        console.log('Comment state synchronized:', {
          original: window.commentState.originalComments,
          deleted: Array.from(window.commentState.deletedCommentIds),
          current: window.commentState.currentComments
        });
      }

      // Use the synchronized pending comments
      const pendingComments = window.pendingComments || {};

      // Send the synchronized comments back to the server
      this.pushEvent('pending_comments_data', { comments: JSON.stringify(pendingComments) });
    });

    // Also handle form submission directly
    const form = modal.querySelector('#comment-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        console.log('Form submission detected');

        // **ENHANCED: Sync comment state before adding new comment**
        const mainTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
        if (mainTextarea && this.parseCommentsFromTextarea) {
          const currentTextareaValue = mainTextarea.value || '';

          // Initialize original state if not done yet
          if (!window.commentState.isInitialized) {
            this.parseCommentsFromTextarea(currentTextareaValue, true);
          } else {
            // Sync current state with textarea content
            const currentParsed = this.parseCommentsFromTextarea(currentTextareaValue);
            window.commentState.currentComments = currentParsed;
          }

          // Maintain backward compatibility
          window.pendingComments = window.commentState.currentComments;
          console.log('Synced comment state before adding new comment:', window.commentState.currentComments);
        }

        // Get the schedule and comment values
        const scheduleInput = form.querySelector('input[name="schedule"]');
        const commentTextarea = form.querySelector('#modal-comment');

        if (scheduleInput && commentTextarea && commentTextarea.value.trim() !== '') {
          console.log('Valid form data found, processing...');

          // Format the schedule name
          const schedule = scheduleInput.value;
          const scheduleName = schedule.includes('schedule_') ?
            `Schedule ${schedule.replace('schedule_', '').toUpperCase()}` :
            schedule;

          // **ENHANCED: Save comment using new state management**
          // Add to current comments (this handles both new and updated comments)
          window.commentState.currentComments[schedule] = commentTextarea.value;

          // Remove from deleted set if it was previously deleted
          window.commentState.deletedCommentIds.delete(schedule);

          // Maintain backward compatibility
          if (!window.pendingComments) window.pendingComments = {};
          window.pendingComments[schedule] = commentTextarea.value;

          console.log('Comment saved with enhanced state tracking:', {
            schedule,
            comment: commentTextarea.value,
            currentState: window.commentState.currentComments,
            deletedIds: Array.from(window.commentState.deletedCommentIds)
          });

          // Format the comment
          const formattedComment = `[${scheduleName}] - ${commentTextarea.value}`;

          // Find the main comment textarea and append the formatted comment
          const mainCommentTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
          if (mainCommentTextarea) {
            // Add a newline if there's already content
            const currentComment = mainCommentTextarea.value;
            const separator = currentComment.trim() !== '' ? '\n' : '';

            // Append the new comment
            mainCommentTextarea.value = currentComment + separator + formattedComment;
            console.log('Updated main comment textarea');
          }
        }

        // Hide the modal immediately for better UX
        console.log('Form submitted, hiding modal');
        setTimeout(() => {
          this.hideModal(modal);
        }, 100);
      });
    } else {
      console.warn('Comment form not found in modal');
    }
  },

  // **SYNC FIX: Helper function to parse comments from textarea content**
  parseCommentsFromTextarea(textareaContent) {
    const parsedComments = {};

    if (!textareaContent || textareaContent.trim() === '') {
      return parsedComments;
    }

    // Split by lines and parse each comment
    const lines = textareaContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine === '') return;

      // Match pattern: [Schedule Name] - comment text
      const match = trimmedLine.match(/^\[(.+?)\]\s*-\s*(.+)$/);
      if (match) {
        const scheduleName = match[1];
        const commentText = match[2];

        // Convert schedule name back to schedule ID
        const scheduleId = this.scheduleNameToId(scheduleName);
        if (scheduleId) {
          parsedComments[scheduleId] = commentText;
        }
      }
    });

    console.log('Parsed comments from textarea:', parsedComments);
    return parsedComments;
  },

  // **SYNC FIX: Helper function to convert schedule name back to ID**
  scheduleNameToId(scheduleName) {
    // Handle different schedule name formats
    if (scheduleName === 'Balance Sheet') return 'bal_sheet';
    if (scheduleName === 'Income Statement') return 'income_stmt';

    // Handle "Schedule XX" format
    const scheduleMatch = scheduleName.match(/^Schedule\s+(.+)$/i);
    if (scheduleMatch) {
      const scheduleNumber = scheduleMatch[1].toLowerCase();
      return `schedule_${scheduleNumber}`;
    }

    // Handle other formats - convert back to underscore format
    return scheduleName.toLowerCase().replace(/\s+/g, '_');
  },

  // **SYNC FIX: Helper function to format schedule ID to display name**
  formatScheduleName(scheduleId) {
    // Handle different schedule ID formats
    if (scheduleId === 'bal_sheet') return 'Balance Sheet';
    if (scheduleId === 'income_stmt') return 'Income Statement';

    // Handle "schedule_xx" format
    if (scheduleId.startsWith('schedule_')) {
      const scheduleNumber = scheduleId.replace('schedule_', '').toUpperCase();
      return `Schedule ${scheduleNumber}`;
    }

    // Handle other formats - convert underscore to title case
    return scheduleId.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }
};



// **COMMENT TEXTAREA HOOK FOR AUTOMATIC UPDATES - ENHANCED WITH SYNC FIX**
Hooks.CommentTextarea = {
  mounted() {
    console.log('CommentTextarea hook mounted for element:', this.el);
    this.updateTextarea();
    this.setupSyncMonitoring();

    // Store reference for debugging
    window.commentTextareaHook = this;
  },

  updated() {
    console.log('CommentTextarea hook updated');
    this.updateTextarea();
  },

  updateTextarea() {
    const comment = this.el.dataset.comment || '';
    console.log('=== TEXTAREA UPDATE ===');
    console.log('Current textarea value:', this.el.value);
    console.log('New comment from data-comment:', comment);
    console.log('Values are different:', this.el.value !== comment);

    // Always update, even if values seem the same (in case of whitespace issues)
    this.el.value = comment;
    this.el.textContent = comment;
    this.el.innerHTML = comment;

    console.log('Textarea value after update:', this.el.value);

    // Trigger visual feedback if there's content
    if (comment && comment.trim() !== '') {
      this.el.style.backgroundColor = '#f0f9ff';
      this.el.style.border = '2px solid #10b981';
      console.log('Applied visual feedback for comment update');

      setTimeout(() => {
        this.el.style.backgroundColor = '';
        this.el.style.border = '';
      }, 2000);
    }

    // Dispatch events to notify other parts of the system
    this.el.dispatchEvent(new Event('input', { bubbles: true }));
    this.el.dispatchEvent(new Event('change', { bubbles: true }));

    console.log('=== END TEXTAREA UPDATE ===');
  },

  // **SYNC FIX: Monitor textarea for manual changes**
  setupSyncMonitoring() {
    let lastKnownValue = this.el.value;

    // Monitor for manual changes with debouncing
    const syncChanges = () => {
      const currentValue = this.el.value;

      if (currentValue !== lastKnownValue) {
        console.log('Manual textarea change detected, syncing pending comments');

        // **ENHANCED: Parse content and update comment state**
        if (window.commentState) {
          // Initialize original state if not done yet
          if (!window.commentState.isInitialized) {
            this.parseCommentsFromTextarea(currentValue, true);
          } else {
            // Parse current content
            const parsedComments = this.parseCommentsFromTextarea(currentValue);

            // Detect deletions by comparing with original comments
            Object.keys(window.commentState.originalComments).forEach(scheduleId => {
              if (!parsedComments[scheduleId]) {
                // Comment was deleted manually
                window.commentState.deletedCommentIds.add(scheduleId);
              }
            });

            // Update current comments
            window.commentState.currentComments = parsedComments;
          }

          // Maintain backward compatibility
          window.pendingComments = window.commentState.currentComments;

          console.log('Updated comment state from manual edit:', {
            current: window.commentState.currentComments,
            deleted: Array.from(window.commentState.deletedCommentIds)
          });

          // **ENHANCED: Notify server with complete state**
          if (this.pushEvent) {
            this.pushEvent('pending_comments_data', {
              comments: JSON.stringify(window.commentState.currentComments),
              deletedCommentIds: JSON.stringify(Array.from(window.commentState.deletedCommentIds)),
              originalComments: JSON.stringify(window.commentState.originalComments)
            });
          }
        }

        lastKnownValue = currentValue;
      }
    };

    // Debounced sync function
    let syncTimeout;
    const debouncedSync = () => {
      clearTimeout(syncTimeout);
      syncTimeout = setTimeout(syncChanges, 500); // 500ms debounce
    };

    // Listen for various change events
    this.el.addEventListener('input', debouncedSync);
    this.el.addEventListener('paste', debouncedSync);
    this.el.addEventListener('keyup', debouncedSync);
    this.el.addEventListener('blur', syncChanges); // Immediate sync on blur

    console.log('Sync monitoring setup complete for textarea');
  },

  // **ENHANCED: Helper function to parse comments and initialize state**
  parseCommentsFromTextarea(textareaContent, initializeOriginal = false) {
    const parsedComments = {};

    if (!textareaContent || textareaContent.trim() === '') {
      return parsedComments;
    }

    // Split by lines and parse each comment
    const lines = textareaContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine === '') return;

      // Match pattern: [Schedule Name] - comment text
      const match = trimmedLine.match(/^\[(.+?)\]\s*-\s*(.+)$/);
      if (match) {
        const scheduleName = match[1];
        const commentText = match[2];

        // Convert schedule name back to schedule ID
        const scheduleId = this.scheduleNameToId(scheduleName);
        if (scheduleId) {
          parsedComments[scheduleId] = commentText;
        }
      }
    });

    // Initialize original state if requested and not already done
    if (initializeOriginal && !window.commentState.isInitialized) {
      window.commentState.originalComments = { ...parsedComments };
      window.commentState.currentComments = { ...parsedComments };
      window.commentState.isInitialized = true;
      console.log('Initialized original comment state:', window.commentState.originalComments);
    }

    return parsedComments;
  },

  // **SYNC FIX: Helper function to convert schedule name back to ID**
  scheduleNameToId(scheduleName) {
    // Handle different schedule name formats
    if (scheduleName === 'Balance Sheet') return 'bal_sheet';
    if (scheduleName === 'Income Statement') return 'income_stmt';

    // Handle "Schedule XX" format
    const scheduleMatch = scheduleName.match(/^Schedule\s+(.+)$/i);
    if (scheduleMatch) {
      const scheduleNumber = scheduleMatch[1].toLowerCase();
      return `schedule_${scheduleNumber}`;
    }

    // Handle other formats - convert back to underscore format
    return scheduleName.toLowerCase().replace(/\s+/g, '_');
  }
};

// Add debugging functions
window.debugCommentSystem = function() {
  console.log('=== COMMENT SYSTEM DEBUG ===');

  // Check if modal exists
  const modal = document.getElementById('comment-modal');
  console.log('Modal found:', !!modal);
  if (modal) {
    console.log('Modal display:', modal.style.display);
    console.log('Modal visibility:', modal.style.visibility);
    console.log('Modal classes:', modal.className);
  }

  // Check if comment buttons exist
  const commentButtons = document.querySelectorAll('[phx-click="open_comment_modal"]');
  console.log('Comment buttons found:', commentButtons.length);

  // Check if hooks are loaded
  console.log('CommentAuth hook loaded:', !!Hooks.CommentAuth);
  console.log('CommentModal hook loaded:', !!Hooks.CommentModal);
  console.log('CommentTextarea hook loaded:', !!Hooks.CommentTextarea);

  // Check global functions
  console.log('showCommentModal function available:', typeof window.showCommentModal);

  // Check main comment field
  const mainCommentField = document.getElementById('comment');
  console.log('Main comment field found:', !!mainCommentField);
  if (mainCommentField) {
    console.log('Main comment field value:', mainCommentField.value);
    console.log('Main comment field data-comment:', mainCommentField.dataset.comment);
  }

  console.log('=== END DEBUG ===');
};

// Add a global test function to verify hooks are loaded
window.testCommentModal = function(schedule = 'schedule_27') {
  console.log('Testing comment modal with schedule:', schedule);

  const scheduleName = schedule.includes('schedule_') ?
    `Schedule ${schedule.replace('schedule_', '').toUpperCase()}` :
    schedule;

  // Try multiple approaches
  console.log('1. Trying direct modal show...');
  if (window.showCommentModal) {
    window.showCommentModal(schedule, scheduleName);
  }

  console.log('2. Trying custom event...');
  window.dispatchEvent(new CustomEvent('show-comment-modal', {
    detail: {
      schedule: schedule,
      scheduleName: scheduleName
    }
  }));

  console.log('3. Trying direct DOM manipulation...');
  const modal = document.getElementById('comment-modal');
  if (modal) {
    modal.style.display = 'block';
    modal.style.visibility = 'visible';
    modal.classList.remove('hidden');
    console.log('Modal should now be visible');
  }
};

console.log('Hooks loaded:', Object.keys(Hooks));
console.log('CommentAuth hook:', Hooks.CommentAuth);
console.log('CommentModal hook:', Hooks.CommentModal);

// **ENHANCED: Add debugging and utility functions for comment state**
window.debugCommentSync = function() {
  console.log('=== COMMENT SYNC DEBUG ===');

  const mainTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
  if (mainTextarea) {
    console.log('Main textarea value:', mainTextarea.value);

    // Parse what's in the textarea
    if (window.commentTextareaHook && window.commentTextareaHook.parseCommentsFromTextarea) {
      const parsed = window.commentTextareaHook.parseCommentsFromTextarea(mainTextarea.value);
      console.log('Parsed comments from textarea:', parsed);
    }
  }

  console.log('Window pending comments:', window.pendingComments);
  console.log('Comment state:', window.commentState);
  console.log('=== END DEBUG ===');
};

// **NEW: Function to reset comment state (useful for testing and cleanup)**
window.resetCommentState = function() {
  window.commentState = {
    originalComments: {},
    deletedCommentIds: new Set(),
    currentComments: {},
    isInitialized: false
  };
  window.pendingComments = {};
  console.log('Comment state reset');
};

// **SYNC FIX: Test function to verify the fix works**
window.testCommentSync = function() {
  console.log('=== TESTING COMMENT SYNC FIX ===');

  // Simulate the bug scenario
  const mainTextarea = document.querySelector('form.comment-form textarea[name="comment"]');
  if (!mainTextarea) {
    console.log('No main textarea found');
    return;
  }

  // Set up test data
  window.pendingComments = {
    'schedule_a': 'Comment A',
    'schedule_b': 'Comment B',
    'schedule_c': 'Comment C'
  };

  mainTextarea.value = '[Schedule A] - Comment A\n[Schedule C] - Comment C';
  console.log('1. Set textarea to have A and C (B manually deleted)');
  console.log('2. Pending comments still has:', window.pendingComments);

  // Trigger sync
  if (window.commentTextareaHook && window.commentTextareaHook.setupSyncMonitoring) {
    mainTextarea.dispatchEvent(new Event('input'));
    setTimeout(() => {
      console.log('3. After sync, pending comments:', window.pendingComments);
      console.log('4. Should only have A and C now (B should be gone)');
      console.log('=== TEST COMPLETE ===');
    }, 600);
  }
};

export default Hooks
